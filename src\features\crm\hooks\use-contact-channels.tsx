import { useQuery } from "@tanstack/react-query";
import {
    getContactChannels,
    ListContactChannelsParams,
} from "../services/portals/contact-channel";

export type UseContactChannelsQuery = Pick<ListContactChannelsParams, "search">;

type UseContactChannelsProps = {
    page?: number;
    pageSize?: number;
    query?: UseContactChannelsQuery;
};

export const useContactChannels = ({
    page = 1,
    pageSize = 20,
    query,
}: UseContactChannelsProps = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["contact-channels", { page, pageSize, query }],
        queryFn: () =>
            getContactChannels({
                page,
                pageSize,
                search: query?.search,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: contactChannels } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        contactChannels,
        COUNT,
    };
};
