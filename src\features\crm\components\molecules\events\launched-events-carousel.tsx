import React, { useRef, useState } from "react";
import {
    Card,
    Empty,
    Button,
    Row,
    Col,
    Progress,
    Statistic,
    Badge,
    Carousel,
    BadgeProps,
} from "antd";
import {
    Clock,
    Users,
    ExternalLink,
    CheckCircle,
    AlertCircle,
    XCircle,
    Play,
    ChevronLeft,
    ChevronRight,
} from "lucide-react";
import dayjs from "dayjs";
import "dayjs/locale/es";
import localizedFormat from "dayjs/plugin/localizedFormat";

dayjs.extend(localizedFormat);
dayjs.locale("es");
import {
    type LaunchedEvent,
    LaunchedEventStatus,
} from "@/features/crm/types/dashboard/events";
import { useNavigate } from "react-router-dom";
import { CarouselRef } from "antd/es/carousel";

interface LaunchedEventsCarouselProps {
    data?: LaunchedEvent[];
}

const LaunchedEventsCarousel: React.FC<LaunchedEventsCarouselProps> = ({
    data = [],
}) => {
    const navigate = useNavigate();
    const carouselRef = useRef<CarouselRef>(null);
    const [currentSlide, setCurrentSlide] = useState(0);

    // Calculate slides per view based on data length and responsive settings
    const SLIDES_PER_VIEW = 1;
    const totalSlides = Math.ceil(data.length / SLIDES_PER_VIEW);

    const handlePrev = () => {
        if (carouselRef.current) {
            carouselRef.current.prev();
        }
    };

    const handleNext = () => {
        if (carouselRef.current) {
            carouselRef.current.next();
        }
    };

    const handleSlideChange = (current: number) => {
        setCurrentSlide(current);
    };

    const formatDate = (dateString: string) => {
        return dayjs(dateString).format("dddd, DD [de] MMMM");
    };

    const formatTime = (dateString: string) => {
        return dayjs(dateString).format("h:mm A");
    };

    const getInvitationProgress = (event: LaunchedEvent) => {
        const total = event.invitationStatus.totalReminders || 1;
        const sent = event.invitationStatus.totalSent || 0;
        return Math.round((sent / total) * 100);
    };

    const handleSeeEventDetails = (event: LaunchedEvent) => {
        navigate(`/crm/event-schedules/${event.esid}`);
    };

    const EventStatusBadge = ({ status }: { status: LaunchedEventStatus }) => {
        const statusText =
            status === LaunchedEventStatus.IN_COURSE
                ? "En Curso"
                : status === LaunchedEventStatus.FUTURE
                  ? "Próximo evento"
                  : "Pasado";

        const badgeStatus: BadgeProps["status"] =
            status === LaunchedEventStatus.IN_COURSE
                ? "processing"
                : status === LaunchedEventStatus.FUTURE
                  ? "success"
                  : "error";

        return (
            <div className="flex items-center gap-2 mb-2">
                <Badge status={badgeStatus} />
                <span className="text-xs font-medium text-blue-600 uppercase tracking-wide">
                    {statusText}
                </span>
            </div>
        );
    };

    const EventCard: React.FC<{ event: LaunchedEvent }> = ({ event }) => (
        <Card className="h-full shadow-lg hover:shadow-xl transition-shadow duration-300">
            {/* Header */}
            <div className="mb-6">
                <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                        <EventStatusBadge status={event.eventStatus} />
                        <h3 className="text-lg font-bold text-gray-800 leading-tight mb-2">
                            {event.eventName}
                        </h3>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            type="link"
                            size="small"
                            icon={<ExternalLink size={14} />}
                            onClick={() => handleSeeEventDetails(event)}
                        >
                            Ver detalles
                        </Button>
                        {event.extEventLink && (
                            <Button
                                type="primary"
                                size="small"
                                icon={<ExternalLink size={14} />}
                                href={event.extEventLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="bg-blue-600 hover:bg-blue-700 border-blue-600"
                            >
                                Unirse
                            </Button>
                        )}
                    </div>
                </div>
            </div>

            <Row>
                <Col md={12} xs={24}>
                    <div className="rounded-lg p-4 mb-4">
                        <div className="flex flex-col gap-4">
                            <Statistic
                                title={
                                    <div className="flex items-center justify-start gap-1">
                                        <Users size={16} className="text-blue-600" />
                                        <span className="text-sm text-gray-600">
                                            Inscripciones
                                        </span>
                                    </div>
                                }
                                value={event.enrollmentCount || 0}
                                valueStyle={{
                                    color: "#1890ff",
                                    fontSize: "24px",
                                    fontWeight: "bold",
                                }}
                            />
                            <div>
                                <div className="flex items-center gap-1 mb-1">
                                    <Play size={14} className="text-green-600" />
                                    <span className="text-xs font-medium text-gray-600">
                                        Inicio
                                    </span>
                                </div>
                                <div className="text-sm font-bold text-gray-800">
                                    {formatDate(event.startDate)}
                                </div>
                                <div className="text-xs text-gray-600">
                                    {formatTime(event.startDate)}
                                </div>
                            </div>
                            <div>
                                <div className="flex items-center gap-1 mb-1">
                                    <Clock size={14} className="text-red-500" />
                                    <span className="text-xs font-medium text-gray-600">
                                        Fin
                                    </span>
                                </div>
                                <div className="text-sm font-bold text-gray-800">
                                    {formatDate(event.endDate)}
                                </div>
                                <div className="text-xs text-gray-600">
                                    {formatTime(event.endDate)}
                                </div>
                            </div>
                        </div>
                    </div>
                </Col>
                <Col md={12} xs={24}>
                    {/* Invitation Progress */}
                    <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700">
                                Progreso de Invitaciones
                            </span>
                            <span className="text-xs text-gray-500">
                                {event.invitationStatus.totalSent || 0} /{" "}
                                {event.invitationStatus.totalReminders || 0}
                            </span>
                        </div>
                        <Progress
                            percent={getInvitationProgress(event)}
                            strokeColor={{
                                "0%": "#1890ff",
                            }}
                            trailColor="#f0f0f0"
                            strokeWidth={8}
                            showInfo={false}
                        />
                    </div>

                    {/* Invitation Status Summary */}
                    <Row gutter={8} className="mb-4">
                        <Col span={8}>
                            <div className="text-center p-2 bg-green-50 rounded-lg">
                                <CheckCircle
                                    size={16}
                                    className="text-green-600 mx-auto mb-1"
                                />
                                <div className="text-xs text-gray-600">Enviados</div>
                                <div className="text-sm font-bold text-green-600">
                                    {event.invitationStatus.totalSent || 0}
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="text-center p-2 bg-orange-50 rounded-lg">
                                <AlertCircle
                                    size={16}
                                    className="text-orange-500 mx-auto mb-1"
                                />
                                <div className="text-xs text-gray-600">Pendientes</div>
                                <div className="text-sm font-bold text-orange-500">
                                    {event.invitationStatus.totalPending || 0}
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="text-center p-2 bg-red-50 rounded-lg">
                                <XCircle
                                    size={16}
                                    className="text-red-500 mx-auto mb-1"
                                />
                                <div className="text-xs text-gray-600">Fallidos</div>
                                <div className="text-sm font-bold text-red-500">
                                    {event.invitationStatus.totalFailed || 0}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </Col>
            </Row>
        </Card>
    );

    return (
        <div className="w-full mb-8">
            {/* Slide Counter - Bottom Left */}
            {data.length > 0 ? (
                <div className="relative">
                    <Carousel
                        ref={carouselRef}
                        infinite={false}
                        slidesToShow={SLIDES_PER_VIEW}
                        slidesToScroll={SLIDES_PER_VIEW}
                        arrows={false}
                        dots={false}
                        afterChange={handleSlideChange}
                    >
                        {data.map((event) => (
                            <div key={event.esid} className="px-2 pb-16">
                                <EventCard event={event} />
                            </div>
                        ))}
                    </Carousel>

                    {/* Custom Controls */}
                    {totalSlides > 1 && (
                        <>
                            {/* Navigation Buttons - Bottom Right */}
                            <div className="absolute bottom-0 right-4 flex gap-2 items-center">
                                <span className="text-sm font-medium text-gray-700">
                                    {data.length} en total
                                </span>
                                <Button
                                    type="primary"
                                    shape="circle"
                                    size="large"
                                    icon={<ChevronLeft size={18} />}
                                    onClick={handlePrev}
                                    disabled={currentSlide === 0}
                                />
                                <Button
                                    type="primary"
                                    shape="circle"
                                    size="large"
                                    icon={<ChevronRight size={18} />}
                                    onClick={handleNext}
                                    disabled={currentSlide >= totalSlides - 1}
                                />
                            </div>
                        </>
                    )}
                </div>
            ) : (
                <div className="text-center py-12">
                    <Empty
                        description="No hay próximos eventos o en curso"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                </div>
            )}
        </div>
    );
};

export default LaunchedEventsCarousel;
