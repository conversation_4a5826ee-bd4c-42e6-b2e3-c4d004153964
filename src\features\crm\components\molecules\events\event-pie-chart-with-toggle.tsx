import React, { useState } from "react";
import { Card, Radio, Empty, RadioChangeEvent } from "antd";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { BarChart3 } from "lucide-react";
import type { EventByType, EventStats } from "@/features/crm/types/dashboard/events";

interface EventPieChartWithToggleProps {
    eventByTypeData?: EventByType;
    eventStatsData?: EventStats;
    isLoading?: boolean;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        color: string;
        name: string;
        value: string | number;
        dataKey: string;
    }>;
    label?: string;
}

interface CustomLabelProps {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
}

const COLORS = [
    "#1890ff",
    "#52c41a",
    "#faad14",
    "#fa541c",
    "#722ed1",
    "#13c2c2",
    "#eb2f96",
];

type ChartMode = "eventType" | "eventState";

const EventPieChartWithToggle: React.FC<EventPieChartWithToggleProps> = ({
    eventByTypeData,
    eventStatsData,
    isLoading = false,
}) => {
    const [chartMode, setChartMode] = useState<ChartMode>("eventType");
    const [helpText, setHelpText] = useState<string>(
        "Programaciones por tipo e inscripciones generadas",
    );

    const getChartData = () => {
        switch (chartMode) {
            case "eventType":
                return eventByTypeData
                    ? [
                          {
                              name: "Workshop",
                              value: eventByTypeData.workshop.count,
                              enrollments: eventByTypeData.workshop.totalEnrollments,
                          },
                          {
                              name: "Webinar",
                              value: eventByTypeData.webinar.count,
                              enrollments: eventByTypeData.webinar.totalEnrollments,
                          },
                          {
                              name: "Taller Práctico",
                              value: eventByTypeData.handsOfWorkshop.count,
                              enrollments:
                                  eventByTypeData.handsOfWorkshop.totalEnrollments,
                          },
                      ]
                    : [];
            case "eventState":
                return eventStatsData
                    ? [
                          {
                              name: "En Planificación",
                              value: eventStatsData.planning,
                          },
                          {
                              name: "Lanzados",
                              value: eventStatsData.launched,
                          },
                          {
                              name: "Inscripciones Cerradas",
                              value: eventStatsData.enrollmentClosed,
                          },
                          {
                              name: "Finalizados",
                              value: eventStatsData.finished,
                          },
                      ]
                    : [];
            default:
                return [];
        }
    };

    const getEnrollmentData = () => {
        if (chartMode !== "eventType" || !eventByTypeData) return [];

        return [
            {
                name: "Workshop - Inscripciones",
                value: eventByTypeData.workshop.totalEnrollments,
                type: "Workshop",
            },
            {
                name: "Webinar - Inscripciones",
                value: eventByTypeData.webinar.totalEnrollments,
                type: "Webinar",
            },
            {
                name: "Taller Práctico - Inscripciones",
                value: eventByTypeData.handsOfWorkshop.totalEnrollments,
                type: "Taller Práctico",
            },
        ];
    };

    const chartData = getChartData();
    const enrollmentData = getEnrollmentData();

    const renderCustomizedLabel = ({
        cx,
        cy,
        midAngle,
        innerRadius,
        outerRadius,
        percent,
    }: CustomLabelProps) => {
        const RADIAN = Math.PI / 180;
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * RADIAN);
        const y = cy + radius * Math.sin(-midAngle * RADIAN);

        return (
            <text
                x={x}
                y={y}
                fill="white"
                textAnchor="middle"
                dominantBaseline="central"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        );
    };

    const getTooltipLabel = () => {
        switch (chartMode) {
            case "eventType":
                return "Eventos";
            case "eventState":
                return "Eventos";
            default:
                return "Cantidad";
        }
    };

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0];
            const isEnrollmentData = data.name?.includes("Inscripciones");

            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{data.name}</p>
                    <p style={{ color: data.color }}>
                        {isEnrollmentData ? "Inscripciones" : getTooltipLabel()}:{" "}
                        {data.value}
                    </p>
                </div>
            );
        }
        return null;
    };

    const handleRadioChange = (e: RadioChangeEvent) => {
        const chartMode: ChartMode = e.target.value;
        setChartMode(chartMode);

        if (chartMode === "eventType") {
            setHelpText("Programaciones por tipo e inscripciones generadas");
        }

        if (chartMode === "eventState") {
            setHelpText("Cantidad de programaciones por estado");
        }
    };

    return (
        <Card
            title={
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <BarChart3 size={20} className="text-blue-500" />
                        <div className="flex flex-col">
                            <span>Programaciones</span>
                            <span className="text-xs text-gray-500">{helpText}</span>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Radio.Group
                            value={chartMode}
                            onChange={handleRadioChange}
                            size="small"
                            buttonStyle="solid"
                        >
                            <Radio.Button value="eventType">
                                Tipo de Evento
                            </Radio.Button>
                            <Radio.Button value="eventState">Estado</Radio.Button>
                        </Radio.Group>
                    </div>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-64">
                {chartData.length && !isLoading ? (
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            {chartMode === "eventType" ? (
                                <>
                                    {/* Inner Pie - Event Types */}
                                    <Pie
                                        data={chartData}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={renderCustomizedLabel}
                                        outerRadius={60}
                                        fill="#8884d8"
                                        dataKey="value"
                                    >
                                        {chartData.map((_, index) => (
                                            <Cell
                                                key={`inner-cell-${index}`}
                                                fill={COLORS[index % COLORS.length]}
                                            />
                                        ))}
                                    </Pie>
                                    {/* Outer Pie - Enrollments */}
                                    <Pie
                                        data={enrollmentData}
                                        cx="50%"
                                        cy="50%"
                                        innerRadius={70}
                                        outerRadius={90}
                                        fill="#82ca9d"
                                        dataKey="value"
                                        labelLine={false}
                                    >
                                        {enrollmentData.map((_, index) => (
                                            <Cell
                                                key={`outer-cell-${index}`}
                                                fill={COLORS[index % COLORS.length]}
                                                fillOpacity={0.7}
                                            />
                                        ))}
                                    </Pie>
                                </>
                            ) : (
                                <Pie
                                    data={chartData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={renderCustomizedLabel}
                                    outerRadius={80}
                                    fill="#8884d8"
                                    dataKey="value"
                                >
                                    {chartData.map((_, index) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={COLORS[index % COLORS.length]}
                                        />
                                    ))}
                                </Pie>
                            )}
                            <Tooltip content={<CustomTooltip />} />
                            {chartMode === "eventType" ? (
                                <Legend
                                    payload={chartData.map((item, index) => ({
                                        value: item.name,
                                        type: "square",
                                        color: COLORS[index % COLORS.length],
                                    }))}
                                />
                            ) : (
                                <Legend />
                            )}
                        </PieChart>
                    </ResponsiveContainer>
                ) : (
                    <div className="h-full flex items-center justify-center">
                        <Empty description="No hay datos disponibles" />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default EventPieChartWithToggle;
