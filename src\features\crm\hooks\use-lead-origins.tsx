import { useQuery } from "@tanstack/react-query";
import { getLeadOrigins, ListLeadOriginsParams } from "../services/portals/lead-origin";

export type UseLeadOriginsQuery = Pick<ListLeadOriginsParams, "search">;

type UseLeadOriginsProps = {
    page?: number;
    pageSize?: number;
    query?: UseLeadOriginsQuery;
};

export const useLeadOrigins = ({
    page = 1,
    pageSize = 20,
    query,
}: UseLeadOriginsProps = {}) => {
    const { data, isLoading, isError, isFetching } = useQuery({
        queryKey: ["lead-origins", { page, pageSize, query }],
        queryFn: () =>
            getLeadOrigins({
                page,
                pageSize,
                search: query?.search,
            }),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: leadOrigins } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading: isLoading || isFetching,
        isError,
        leadOrigins,
        COUNT,
    };
};
